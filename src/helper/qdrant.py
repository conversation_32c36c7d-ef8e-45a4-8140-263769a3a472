from qdrant_client.http.models import Record  # Qdrant Record type
from llama_index.core.schema import Document
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import asyncio
import json
import time
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
from qdrant_client import QdrantClient
from qdrant_client.http import models
from llama_index.core.schema import NodeWithScore, TextNode
from llama_index.core.response import Response
from src.helper.logger import setup_new_logging
from src.helper.file_processing import get_presigned_url_for_files
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.reply.minio_client import MinIOClient, MinIOConfig
from bson import ObjectId
loggers = setup_new_logging(__name__)

# Utility functions
async def get_payload_text(item, minio):
    """Process each Qdrant item concurrently"""
    node = item.dict()["payload"]["_node_content"]
    node = json.loads(node)
    text = node["text_resource"]["text"]
    return text

async def process_qdrant_item(item, minio, current_user):
    """Process each Qdrant item concurrently"""
    extra_info = item.payload
    node = item.dict()["payload"]["_node_content"]
    extra_info.pop("_node_content")
    extra_info["images_url"] = []
    extra_info["source_url"] = ""
    source = extra_info.get("source")
    
    # Create tasks for concurrent execution
    tasks = []
    if source := extra_info.get("source"):
        tasks.append(get_presigned_url_for_files([source], minio, "Files"))
    if images := extra_info.get("images"):
        tasks.append(get_presigned_url_for_files(images, minio, f"Images/{source}"))

    # Execute tasks concurrently
    results = await asyncio.gather(*tasks)

    # Process results
    if source:
        try:
            extra_info["source_url"] = results[0][0]
        except Exception as e:
            print(e)
    if images:
        extra_info["images_url"] = results[-1]

    node = json.loads(node)
    updated_by=extra_info.get("updated_by")
    if updated_by:
        user=current_user.db.users.find_one({"_id":ObjectId(updated_by)})
        if user:
            updated_by=user.get("username")
    return {
        "id_": extra_info.get("hash_id"),
        "extra_info": extra_info,
        "text": node["text_resource"]["text"] if node.get("text_resource") else node["text"],
        "created_at": extra_info.get("created_at"),
        "updated_at": extra_info.get("updated_at"),
        "resource_url": extra_info.get("resource_url"),
        "updated_by": updated_by,
    }

# Document processing functions
def process_record(record: Record) -> Document:
    """Helper function to process a single Qdrant Record and convert it into a LlamaIndex Document."""
    payload = record.payload
    data = json.loads(payload.get("_node_content"))
    if not payload:
        return None

    text = data.get("text_resource", {}).get("text", "")
    metadata = data.get("metadata", {})
    metadata = {k: v for k, v in metadata.items() if k not in ["source_url", "images_url"]}
    
    return Document(
        text=text,
        metadata=metadata,
        id_=str(record.id),
    )

def convert_records_to_documents(records: List[Record]) -> List[Document]:
    """Converts a list of Qdrant Record objects into a list of LlamaIndex Document objects."""
    documents: List[Document] = []
    with ThreadPoolExecutor() as executor:
        future_to_record = {executor.submit(process_record, record): record for record in records}
        for future in as_completed(future_to_record):
            try:
                document = future.result()
                if document:
                    documents.append(document)
            except Exception as e:
                print(f"Error processing record: {e}")
    return documents

# Search and processing functions
async def fetch_qdrant_config(current_user):
    """Fetch Qdrant and MinIO configurations from the database."""
    env_var = current_user.db.settings.find_one({"name": "env"})
    return env_var.get("qdrant_config", {}), env_var.get("minio_config", {})

async def initialize_clients(qdrant_config, minio_config):
    """Initialize Qdrant and MinIO clients."""
    print(qdrant_config)
    qarant = None
    minio = None
    if qdrant_config:
        qarant = Qdrant_Call(
            config=QdrantConfig(
                host=qdrant_config["host"],
            port=qdrant_config["port"],
            coll_name=qdrant_config["sentence_collection"],
        )
    )
    if minio_config:
        minio = MinIOClient(
            config=MinIOConfig(
                access_key=minio_config.get("access_key"),
                secret_key=minio_config.get("secret_key"),
                minio_url=minio_config.get("minio_url"),
                bucket_name=minio_config.get("bucket_name"),
            )
        )
    return qarant, minio

async def fetch_sentence_text(v, qarant, qdrant_config, minio):
    """Retrieve sentence text if 'sent_id' exists."""
    if not v.get("sent_id"):
        return None

    sent, _ = qarant.client_().scroll(
        collection_name=qdrant_config["sentence_split_collection"],
        scroll_filter=models.Filter(
            must=[
                models.FieldCondition(key="sent_id", match=models.MatchValue(value=v["sent_id"]))
            ]
        ),
        limit=1,
    )

    if sent:
        return await get_payload_text(sent[0], minio)
    return None

async def fetch_filter_nodes(v, qarant, qdrant_config):
    """Retrieve nodes from the Qdrant page collection based on metadata filters."""
    filter_conditions = [
        models.FieldCondition(key=k, match=models.MatchValue(value=v))
        for k, v in v.items()
        if k not in {"images", "title_embedding", "sent_id", "images_url", "source_url"} and v
    ]

    filter_node, _ = qarant.client_().scroll(
        collection_name=qdrant_config["page_collection"],
        limit=1,
        scroll_filter=models.Filter(must=filter_conditions),
    )

    return filter_node

async def process_metadata_item(k, v, qarant, qdrant_config, minio, filtered_nodes, processed_nodes, current_user):
    """Process metadata item, fetch nodes, and associate sentence text."""
    source, page_number = v.get("source"), v.get("page_number")
    key = f"{source}-{page_number}"
    sent_text = await fetch_sentence_text(v, qarant, qdrant_config, minio)
    
    if key in processed_nodes:
        if sent_text:
            for node in filtered_nodes:
                if node["id_"] == filter_node[0].id:
                    node["sentence"].append(sent_text)
    else:
        processed_nodes.add(key)
        filter_node = await fetch_filter_nodes(v, qarant, qdrant_config)
        nodes = await asyncio.gather(*[process_qdrant_item(i, minio, current_user) for i in filter_node])
        for node in nodes:
            node["sentence"] = [sent_text] if sent_text else []
            filtered_nodes.append(node)

async def process_metadata(response, qarant, qdrant_config, minio, current_user):
    """Process all metadata items in parallel."""
    filtered_nodes = []
    processed_nodes = set()
    if response.metadata:
        await asyncio.gather(
            *[
                process_metadata_item(k, v, qarant, qdrant_config, minio, filtered_nodes, processed_nodes, current_user)
                for k, v in response.metadata.items()
            ]
        )
    return filtered_nodes

async def process_source_urls(response, minio):
    """Generate presigned URLs for sources in metadata."""
    filtered_source = set()

    async def process_source(k, v):
        if v.get("source"):
            source_urls = await get_presigned_url_for_files([v["source"]], minio, "Files")
            for _, url in source_urls:
                filtered_source.add(url)
    if response.metadata:
        await asyncio.gather(*[process_source(k, v) for k, v in response.metadata.items()])
    return list(filtered_source)

# Page and sentence processing
async def extract_page_sent(data, qdrant_config, qdrant, minio, current_user):
    page_data = defaultdict(lambda: defaultdict(list))
    print("dataa", data)
    for node in data:
        if isinstance(node, dict):
            node = NodeWithScore(**node)

        score = node.score
        metadata = node.node.metadata
        metadata["score"] = score
        page_number = metadata.get("page_number")
        source = metadata.get("source", "Unknown Source")
        code = metadata.get("code", "code not found")
        title = metadata.get("title")

        if page_number is not None:
            page_data[source][page_number].append(metadata)

    final_nodes = await asyncio.gather(*[
        process_page(
            source=metadata_list[0].get("code") if current_user.slug == "agbooks" else source,
            hash_id=metadata_list[0].get("hash_id"),
            page_number=page_number,
            title=metadata_list[0].get("title"),
            metadata_list=metadata_list,
            qdrant=qdrant,
            minio=minio,
            qdrant_config=qdrant_config,
            current_user=current_user
        )
        for source, pages in page_data.items()
        for page_number, metadata_list in pages.items()
    ])

    return final_nodes

async def process_page(source: str, page_number: int,title:str,hash_id:str, metadata_list: List[Dict], qdrant: QdrantClient, minio, qdrant_config, current_user):
    try:
        # Determine the source field based on user context
        source_field = "code" if current_user.slug == "agbooks" else "source"
        
        # Build filter to find the exact page
        page_filter = models.Filter(
            must=[
                models.FieldCondition(key=source_field, match=models.MatchValue(value=source)),
                models.FieldCondition(key="title", match=models.MatchValue(value=title)),
                models.FieldCondition(key="hash_id", match=models.MatchValue(value=hash_id)),
                models.FieldCondition(key="page_number", match=models.MatchValue(value=page_number)),
            ]
        )

        # Retrieve the page from Qdrant
        page_records, _ = qdrant.scroll(
            collection_name=qdrant_config.get("page_collection"),
            scroll_filter=page_filter,
        )

        if not page_records:
            loggers.warning(
                f"No page found for source={source}, title={title}, "
                f"page_number={page_number}, hash_id={hash_id}"
            )
            return None

        # Process the page record
        page_node = await process_qdrant_item(page_records[0], minio, current_user)
        if not page_node:
            return None

        # Prepare the node structure
        page_node["metadata"] = page_node.get("extra_info", {})
        del page_node["extra_info"]
        page_node["metadata"]["sentence"] = []
        
        # Extract unique sentence IDs from metadata that belong to this specific page
        valid_sent_ids = {
            m.get("sent_id") for m in metadata_list 
            if m.get("sent_id") and m.get(source_field) == source
        }

        # Process all sentences concurrently
        sentences = await asyncio.gather(*[
            process_sentence(
                sent_id=sent_id,
                qdrant=qdrant,
                minio=minio,
                qdrant_config=qdrant_config,
            )
            for sent_id in valid_sent_ids
        ])

        # Filter out None and empty strings, ensure sentences belong to this source
        page_node["metadata"]["sentence"] = [
            sent for sent in sentences 
            if sent and sent.strip()
        ]

        # Get the highest score from metadata_list for this page
        score = max((m.get("score", 0.0) for m in metadata_list), default=0.0)

        return NodeWithScore(
            node=TextNode(**page_node),
            score=score
        )

    except Exception as e:
        loggers.error(
            f"Error processing page (source={source}, page={page_number}): {str(e)}\n"
            f"{traceback.format_exc()}"
        )
        return None

async def process_sentence(sent_id: str, qdrant, minio, qdrant_config):
    print("\n\n sent_iddds",sent_id)
    sent_filter = models.Filter(
        must=[models.FieldCondition(key="sent_id", match=models.MatchValue(value=sent_id))]
    )
    sent, _ = qdrant.scroll(collection_name=qdrant_config.get("sentence_split_collection"), scroll_filter=sent_filter)
    if sent:
        return await get_payload_text(sent[0], minio)
    else:
        loggers.debug(f"No sentence found for {sent_id}")
        return None

