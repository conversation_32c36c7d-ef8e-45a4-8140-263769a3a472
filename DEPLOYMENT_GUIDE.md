# 🚀 Eko Backend Deployment Guide

## 📋 Overview

This guide covers the deployment setup for Eko Backend with Traefik load balancer, featuring:
- **Port 8201**: API Documentation and General Access
- **Port 8202**: Traefik Dashboard
- **Sticky Sessions**: Ensures document processing and WebSocket connections use the same instance
- **Document Processing Flow**: Proper routing for process-documents → setup_files WebSocket flow

## 🌐 Port Configuration

| Port | Service | Description |
|------|---------|-------------|
| 80 | HTTP | Main HTTP entry point (redirects to HTTPS) |
| 443 | HTTPS | Main HTTPS API access |
| 8201 | API Docs | API documentation and general HTTP access |
| 8202 | Traefik Dashboard | Traefik monitoring and configuration dashboard |

## 🔄 Document Processing Flow

### Critical Flow Requirements
1. **Process Documents First**: Client calls `/process-documents` endpoint
2. **WebSocket Connection**: Client connects to `/setup_files` WebSocket for status updates
3. **Single Instance**: Sticky sessions ensure both requests hit the same application instance

### Flow Diagram
```
Client → WebSocket(/setup_files) → Same Instance
   ↓
Client → POST(/process-documents) → Same Instance (via sticky session)
   ↓
Real-time status updates via WebSocket
```

## 🏗️ Architecture Components

### Traefik Configuration
- **Main Config**: `config/traefik.yaml`
- **Middlewares**: `config/conf/middlewares.yaml`
- **Entry Points**: web, websecure, http-api, traefik-dashboard

### Sticky Sessions Configuration
```yaml
# Ensures same instance handles related requests
traefik.http.services.eko-api-service.loadbalancer.sticky.cookie=true
traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.name=eko-session
traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.maxAge=3600
```

### WebSocket Support
- **Endpoint**: `/setup_files`
- **Authentication**: Token via query parameter
- **Connection Manager**: Handles tenant-based connections
- **Status Updates**: Real-time JSON messages

## 🚀 Deployment Steps

### 1. Environment Setup
```bash
# Set your domain
export DOMAIN=your-domain.com

# Ensure SSL certificates are in place
ls -la /etc/letsencrypt/live/eko-api2.nextai.asia/
```

### 2. Start Services
```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### 3. Verify Deployment
```bash
# Check API health
curl http://localhost:8201/health

# Check Traefik dashboard
curl http://localhost:8202/api/overview

# Test WebSocket connection
curl -H "Upgrade: websocket" -H "Connection: Upgrade" \
     "ws://localhost:8201/setup_files?token=YOUR_TOKEN"
```

## 🔧 API Endpoints

### Core Document Processing
- `POST /process-documents` - Process files/URLs
- `WebSocket /setup_files` - Real-time status updates
- `GET /check_status` - Check WebSocket connection status

### API Documentation (Port 8201)
- `GET /api-info` - API overview and endpoint listing
- `GET /flow-diagram` - Visual processing flow diagram
- `GET /health-detailed` - Detailed health check

### Health & Monitoring
- `GET /health` - Basic health check
- `GET /docs` - FastAPI documentation (Swagger UI)

## 🔒 Security Features

### Middleware Stack
- **CORS**: Configured for cross-origin requests
- **Security Headers**: XSS protection, frame denial, HSTS
- **Rate Limiting**: 100 requests/minute with burst of 50
- **Compression**: Automatic response compression

### Authentication
- **Token-based**: WebSocket connections require valid token
- **Tenant Isolation**: Connections managed by tenant_id
- **Session Management**: Automatic cleanup on disconnect

## 🐛 Troubleshooting

### Common Issues

#### WebSocket Connection Fails
```bash
# Check if WebSocket route is properly configured
docker-compose logs traefik | grep websocket

# Verify token is valid
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8201/check_status
```

#### Sticky Sessions Not Working
```bash
# Check cookie configuration
curl -v http://localhost:8201/api-info

# Look for Set-Cookie header with eko-session
```

#### Port Access Issues
```bash
# Verify ports are exposed
docker-compose ps
netstat -tlnp | grep -E ':(8201|8202)'
```

### Logs and Monitoring
```bash
# Application logs
docker-compose logs eko-api

# Traefik logs
docker-compose logs traefik

# Real-time log monitoring
docker-compose logs -f
```

## 📊 Performance Considerations

### Load Balancing
- Sticky sessions ensure state consistency
- Health checks prevent routing to unhealthy instances
- Circuit breaker protects against cascading failures

### WebSocket Optimization
- Connection pooling by tenant_id
- Automatic cleanup of disconnected sessions
- JSON message format for efficient data transfer

### Resource Management
- Memory-efficient document processing
- Streaming responses for large files
- Automatic garbage collection of temporary files

## 🔄 Scaling Considerations

### Horizontal Scaling
- Multiple API instances supported
- Sticky sessions maintain state consistency
- Shared storage for document processing

### Monitoring
- Prometheus metrics enabled
- Health check endpoints for load balancer
- Detailed logging for debugging

---

**Note**: This setup ensures that the document processing flow works correctly with sticky sessions, maintaining the critical requirement that process-documents and setup_files WebSocket connections use the same application instance.
