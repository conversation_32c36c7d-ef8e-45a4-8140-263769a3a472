---
# Traefik Configuration with Multiple Instances and Sticky Sessions
# Supports WebSocket connections and document processing with session affinity

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API docs port
      - "8202:8202"   # Traefik dashboard port
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      # Traefik configuration
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      # Use existing Let's Encrypt certificates directly
      - /etc/letsencrypt/live/eko-api2.nextai.asia/:/etc/letsencrypt/live/eko-api2.nextai.asia/:ro
    environment:
      - DOMAIN=${DOMAIN:-localhost}
    command:
      - /bin/sh
      - -c
      - |
        echo "🌐 DOMAIN: $$DOMAIN"
        echo "📜 Using existing Let's Encrypt certificates from /etc/letsencrypt/live/eko-api2.nextai.asia/"
        echo "✅ No ACME setup needed - avoiding rate limits completely"
        traefik --configfile=/etc/traefik/traefik.yaml
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"
      # Traefik dashboard on port 8202 - works with or without domain
      - "traefik.http.routers.traefik-dashboard.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=traefik-dashboard"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.middlewares=api-cors@file"

  # Scalable API Application Instances with Sticky Sessions
  # Use: docker-compose up --scale eko-api=3 -d
  eko-api:
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
      - INSTANCE_ID=${HOSTNAME:-eko-api}
    # extra_hosts:
    #   - "minio.nextai.asia:*************"
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"

      # Main HTTPS API routes - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.api-secure.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.api-secure.entrypoints=websecure}"
      - "${DOMAIN:+traefik.http.routers.api-secure.tls=true}"
      - "${DOMAIN:+traefik.http.routers.api-secure.service=eko-api-service}"
      - "${DOMAIN:+traefik.http.routers.api-secure.middlewares=default-headers@file,api-cors@file}"

      # CRITICAL: Document processing endpoints - MUST use same instance
      # Process documents endpoint - highest priority
      - "traefik.http.routers.process-docs.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/process-documents`)"
      - "traefik.http.routers.process-docs.entrypoints=websecure,http-api"
      - "traefik.http.routers.process-docs.service=eko-api-service"
      - "traefik.http.routers.process-docs.middlewares=api-cors@file"
      - "traefik.http.routers.process-docs.priority=100"

      # WebSocket setup_files endpoint - MUST use same instance as process-documents
      - "traefik.http.routers.websocket.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/setup_files`)"
      - "traefik.http.routers.websocket.entrypoints=websecure,http-api"
      - "traefik.http.routers.websocket.service=eko-api-service"
      - "traefik.http.routers.websocket.middlewares=websocket-headers@file"
      - "traefik.http.routers.websocket.priority=99"

      # Check status endpoint - same instance as above
      - "traefik.http.routers.check-status.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/check_status`)"
      - "traefik.http.routers.check-status.entrypoints=websecure,http-api"
      - "traefik.http.routers.check-status.service=eko-api-service"
      - "traefik.http.routers.check-status.middlewares=api-cors@file"
      - "traefik.http.routers.check-status.priority=98"

      # General API routes - lower priority
      - "traefik.http.routers.api-docs.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.api-docs.entrypoints=http-api"
      - "traefik.http.routers.api-docs.service=eko-api-service"
      - "traefik.http.routers.api-docs.middlewares=api-cors@file,compression@file"
      - "traefik.http.routers.api-docs.priority=1"

      # Service configuration with sticky sessions for document processing and WebSocket
      - "traefik.http.services.eko-api-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.timeout=10s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.scheme=http"

      # Sticky sessions configuration - CRITICAL for document processing flow
      # Ensures process-documents and setup_files WebSocket go to same instance
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.name=eko-session"
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.path=/"
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.secure=false"  # Allow HTTP for port 8201
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.httpOnly=false"  # Allow JS access for WebSocket
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.sameSite=lax"
      - "traefik.http.services.eko-api-service.loadbalancer.sticky.cookie.maxAge=7200"  # 2 hour session

      # Load balancer strategy - ensures consistent routing
      - "traefik.http.services.eko-api-service.loadbalancer.passhostheader=true"

networks:
  eko-network:
    driver: bridge


